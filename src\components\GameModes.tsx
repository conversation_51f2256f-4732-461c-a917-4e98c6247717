import React from 'react';
import type { GameMode } from '../types/game.types';

interface GameModesProps {
  onSelectMode: (mode: GameMode) => void;
  onBack: () => void;
}

const gameModes: GameMode[] = [
  {
    id: 'quick',
    name: 'التحدي السريع',
    description: '10 أسئلة في 5 دقائق',
    icon: '⚡',
    timeLimit: 300,
    questionCount: 10,
    isTimeBased: true
  },
  {
    id: 'classic',
    name: 'الوضع الكلاسيكي',
    description: '20 سؤال بدون وقت محدد',
    icon: '🎯',
    questionCount: 20,
    isTimeBased: false
  }
];

const GameModes: React.FC<GameModesProps> = ({ onSelectMode, onBack }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-2">
            اختر نمط اللعب
          </h2>
          <p className="text-gray-600">
            اختر التحدي المناسب لك
          </p>
        </div>

        {/* Game Modes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {gameModes.map((mode) => (
            <div
              key={mode.id}
              onClick={() => onSelectMode(mode)}
              className="p-6 border-2 border-gray-200 hover:border-blue-400 rounded-lg cursor-pointer transition-all hover:shadow-lg"
            >
              <div className="text-center">
                <div className="text-4xl mb-3">{mode.icon}</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {mode.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {mode.description}
                </p>

                {/* Mode Details */}
                <div className="flex justify-center gap-4 text-sm">
                  <div className="flex items-center">
                    <span className="text-blue-600">📝</span>
                    <span className="mr-1">{mode.questionCount} سؤال</span>
                  </div>
                  {mode.isTimeBased && mode.timeLimit && (
                    <div className="flex items-center">
                      <span className="text-red-600">⏰</span>
                      <span className="mr-1">
                        {Math.floor(mode.timeLimit / 60)} دقيقة
                      </span>
                    </div>
                  )}
                </div>

                {/* Difficulty Indicator */}
                <div className="mt-4">
                  {mode.id === 'quick' && (
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      سهل
                    </span>
                  )}
                  {mode.id === 'classic' && (
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      متوسط
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Back Button */}
        <div className="text-center">
          <button
            onClick={onBack}
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
          >
            ← العودة للقائمة الرئيسية
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameModes;
