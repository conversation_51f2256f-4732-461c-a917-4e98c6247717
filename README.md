# 🧠 QuizRush - لعبة الأسئلة العربية

![QuizRush](https://img.shields.io/badge/QuizRush-🧠-blue?style=for-the-badge)
![React](https://img.shields.io/badge/React-19.1.0-61DAFB?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-3178C6?style=for-the-badge&logo=typescript)
![Vite](https://img.shields.io/badge/Vite-6.3.5-646CFF?style=for-the-badge&logo=vite)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.1.8-38B2AC?style=for-the-badge&logo=tailwind-css)

## 📝 وصف اللعبة

QuizRush هي لعبة أسئلة عربية تفاعلية مصممة خصيصاً للجمهور العربي. تقدم تجربة لعب خفيفة وسريعة وممتعة بدون الحاجة لتسجيل دخول أو تخزين بيانات.

## 🎮 المميزات

✅ **واجهة عربية 100%** مع دعم RTL
✅ **أسئلة متنوعة** في مجالات مختلفة
✅ **أنماط لعب متعددة** (سريع، كلاسيكي)
✅ **تصميم responsive** لجميع الأجهزة
✅ **تغذية راجعة فورية** مع تفسيرات
✅ **نظام نقاط** وتتبع الأداء
✅ **لا يتطلب تسجيل دخول**

## 🛠️ التقنيات المستخدمة

- **React 19** - مكتبة واجهة المستخدم
- **TypeScript** - للكتابة الآمنة
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل CSS
- **Cairo Font** - خط عربي جميل

## 🕹️ أنماط اللعب

### ⚡ التحدي السريع
- 5 أسئلة متنوعة
- مثالي للمبتدئين
- تغذية راجعة فورية

### 🎯 الوضع الكلاسيكي
- 5 أسئلة بدون وقت محدد
- مثالي للتعلم والتركيز
- تفسيرات مفصلة

## 🚀 تشغيل اللعبة

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd my-react-app
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل الخادم المحلي**
```bash
npm run dev
```

4. **فتح اللعبة**
افتح المتصفح وانتقل إلى: `http://localhost:5173`

## 📁 هيكل المشروع

```
src/
├── components/          # مكونات React
│   ├── GameMenu.tsx    # القائمة الرئيسية
│   ├── GameModes.tsx   # اختيار أنماط اللعب
│   ├── QuizGame.tsx    # واجهة اللعب الرئيسية
│   ├── Question.tsx    # عرض السؤال والخيارات
│   ├── Timer.tsx       # مؤقت اللعبة
│   ├── ScoreBoard.tsx  # عرض النتائج
│   └── HighScores.tsx  # أفضل النتائج
├── data/               # بيانات اللعبة
│   ├── questions.json  # قاعدة بيانات الأسئلة
│   └── categories.ts   # تصنيفات الأسئلة
├── hooks/              # Custom Hooks
│   ├── useGameLogic.ts # منطق اللعبة
│   └── useTimer.ts     # إدارة المؤقت
├── types/              # تعريف الأنواع
│   └── game.types.ts   # أنواع البيانات
├── utils/              # وظائف مساعدة
│   └── gameUtils.ts    # وظائف مساعدة للعبة
└── App.tsx             # المكون الرئيسي
```

## 🎮 كيفية اللعب

### البداية
1. اختر نمط اللعب المناسب لك من القائمة الرئيسية
2. اقرأ السؤال بعناية
3. اختر الإجابة الصحيحة من الخيارات المتاحة
4. احصل على تغذية راجعة فورية مع التفسير

### أنماط اللعب المختلفة
- **التحدي السريع** 🚀: مثالي للمبتدئين
- **الوضع الكلاسيكي** 🎯: للتعلم بدون ضغط الوقت
- **تحدي الخبراء** 🔥: للاعبين المتقدمين
- **الماراثون** 🏃‍♂️: لاختبار المعرفة الشاملة

### نظام النقاط
- **نقاط الإجابة الصحيحة**: حتى 1000 نقطة
- **مكافأة السرعة**: حتى 500 نقطة إضافية
- **النقاط الإجمالية**: مجموع النقاط + مكافأة السرعة

## 🏆 المساهمة

نرحب بمساهماتكم لتطوير اللعبة! يمكنكم:

1. **إضافة أسئلة جديدة** في ملف `src/data/questions.json`
2. **تحسين التصميم** أو إضافة مؤثرات جديدة
3. **إضافة مميزات جديدة** مثل أنماط لعب إضافية
4. **تحسين الأداء** أو إصلاح الأخطاء

### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:

- 🐛 **الإبلاغ عن خطأ**: افتح Issue جديد
- 💡 **اقتراح ميزة**: شارك أفكارك معنا
- 📧 **التواصل**: راسلنا عبر البريد الإلكتروني

## 🙏 شكر وتقدير

- **فريق React** لمكتبة React الرائعة
- **فريق Vite** لأداة البناء السريعة
- **فريق Tailwind CSS** لإطار العمل المرن
- **المجتمع العربي** للإلهام والدعم

---

<div align="center">

**صُنع بـ ❤️ للمجتمع العربي**

[⭐ أعطنا نجمة](https://github.com/your-repo) • [🐛 الإبلاغ عن خطأ](https://github.com/your-repo/issues) • [💡 اقتراح ميزة](https://github.com/your-repo/issues)

</div>
