import React from 'react';

interface GameMenuProps {
  onStartGame: () => void;
  onViewHighScores: () => void;
}

const GameMenu: React.FC<GameMenuProps> = ({ onStartGame, onViewHighScores }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
        {/* Logo and Title */}
        <div className="mb-8">
          <div className="text-6xl mb-4">🧠</div>
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            QuizRush
          </h1>
          <p className="text-lg text-gray-600">
            لعبة الأسئلة العربية التفاعلية
          </p>
        </div>

        {/* Description */}
        <div className="mb-8 text-gray-700">
          <p className="mb-4">
            اختبر معلوماتك في مختلف المجالات
          </p>
          <div className="flex justify-center gap-2">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
              ثقافة عامة
            </span>
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              تاريخ
            </span>
            <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
              علوم
            </span>
          </div>
        </div>

        {/* Menu Buttons */}
        <div className="space-y-4">
          <button
            onClick={onStartGame}
            className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg w-full transition-colors"
          >
            🎮 ابدأ اللعب
          </button>

          <button
            onClick={onViewHighScores}
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg w-full transition-colors"
          >
            🏆 أفضل النتائج
          </button>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl mb-2">⚡</div>
            <p className="text-sm text-gray-600">سريع وممتع</p>
          </div>
          <div>
            <div className="text-2xl mb-2">🎯</div>
            <p className="text-sm text-gray-600">أسئلة متنوعة</p>
          </div>
          <div>
            <div className="text-2xl mb-2">🏅</div>
            <p className="text-sm text-gray-600">تحدي ذكي</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-sm text-gray-500">
          <p>لا يتطلب تسجيل دخول • العب مباشرة</p>
        </div>
      </div>
    </div>
  );
};

export default GameMenu;
