import React, { useState } from 'react';
import type { GameMode } from '../types/game.types';

interface SimpleQuizProps {
  gameMode: GameMode;
  onBackToModes: () => void;
}

const questions = [
  {
    id: 1,
    question: "ما هي عاصمة المملكة العربية السعودية؟",
    options: ["الرياض", "جدة", "مكة المكرمة", "الدمام"],
    correctAnswer: 0,
    explanation: "الرياض هي العاصمة الرسمية للمملكة العربية السعودية."
  },
  {
    id: 2,
    question: "من هو أول خليفة راشد؟",
    options: ["عمر بن الخطاب", "أبو بكر الصديق", "عثمان بن عفان", "علي بن أبي طالب"],
    correctAnswer: 1,
    explanation: "أبو بكر الصديق رضي الله عنه هو أول الخلفاء الراشدين."
  },
  {
    id: 3,
    question: "كم عدد أيام السنة الميلادية؟",
    options: ["364", "365", "366", "367"],
    correctAnswer: 1,
    explanation: "السنة الميلادية العادية تحتوي على 365 يوماً."
  },
  {
    id: 4,
    question: "ما هو أطول نهر في العالم؟",
    options: ["نهر الأمازون", "نهر النيل", "نهر الميسيسيبي", "نهر اليانغتسي"],
    correctAnswer: 1,
    explanation: "نهر النيل هو أطول نهر في العالم بطول يبلغ حوالي 6650 كيلومتر."
  },
  {
    id: 5,
    question: "ما هو العنصر الكيميائي الذي رمزه Au؟",
    options: ["الفضة", "الذهب", "الألومنيوم", "الأرجون"],
    correctAnswer: 1,
    explanation: "Au هو الرمز الكيميائي للذهب، مشتق من الكلمة اللاتينية Aurum."
  }
];

const SimpleQuiz: React.FC<SimpleQuizProps> = ({ gameMode, onBackToModes }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [score, setScore] = useState(0);
  const [gameFinished, setGameFinished] = useState(false);

  const question = questions[currentQuestion];

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    
    setSelectedAnswer(answerIndex);
    setShowAnswer(true);
    
    if (answerIndex === question.correctAnswer) {
      setScore(score + 1);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setShowAnswer(false);
    } else {
      setGameFinished(true);
    }
  };

  const getOptionClass = (optionIndex: number) => {
    let baseClass = "w-full p-4 text-right rounded-lg border-2 transition-all font-medium cursor-pointer ";
    
    if (!showAnswer) {
      return baseClass + "border-gray-200 bg-white hover:bg-blue-50 hover:border-blue-300 text-gray-700";
    }

    if (optionIndex === question.correctAnswer) {
      return baseClass + "bg-green-100 border-green-500 text-green-800";
    }
    
    if (selectedAnswer === optionIndex && optionIndex !== question.correctAnswer) {
      return baseClass + "bg-red-100 border-red-500 text-red-800";
    }

    return baseClass + "border-gray-200 bg-gray-50 text-gray-500";
  };

  if (gameFinished) {
    const accuracy = (score / questions.length) * 100;
    
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="text-6xl mb-4">
            {accuracy >= 80 ? '🎉' : accuracy >= 60 ? '👏' : '💪'}
          </div>
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            انتهت اللعبة!
          </h2>
          
          <div className="mb-6">
            <div className="text-4xl font-bold text-blue-600 mb-2">
              {score} / {questions.length}
            </div>
            <p className="text-gray-600">إجابات صحيحة</p>
          </div>

          <div className="mb-6">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {accuracy.toFixed(1)}%
            </div>
            <p className="text-gray-600">نسبة الدقة</p>
          </div>

          <div className="space-y-4">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg w-full transition-colors"
            >
              🎮 العب مرة أخرى
            </button>
            
            <button
              onClick={onBackToModes}
              className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg w-full transition-colors"
            >
              🏠 العودة لاختيار النمط
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 p-4">
      {/* Header */}
      <div className="max-w-4xl mx-auto mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="text-2xl">{gameMode.icon}</div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">
                  {gameMode.name}
                </h1>
                <p className="text-sm text-gray-600">
                  السؤال {currentQuestion + 1} من {questions.length}
                </p>
              </div>
            </div>

            <button
              onClick={onBackToModes}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              ✕
            </button>
          </div>

          {/* Progress */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-8 leading-relaxed">
            {question.question}
          </h2>

          <div className="space-y-4 mb-6">
            {question.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                disabled={showAnswer}
                className={getOptionClass(index)}
              >
                <div className="flex justify-between items-center">
                  <span className="flex-1 text-right">{option}</span>
                  <span className="text-xl mr-3">
                    {showAnswer && index === question.correctAnswer ? '✅' : 
                     showAnswer && selectedAnswer === index && index !== question.correctAnswer ? '❌' : ''}
                  </span>
                </div>
              </button>
            ))}
          </div>

          {/* Explanation */}
          {showAnswer && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border-r-4 border-blue-500">
              <div className="flex items-start gap-2">
                <span className="text-blue-600 text-xl">💡</span>
                <div>
                  <h4 className="font-semibold text-blue-800 mb-1">تفسير:</h4>
                  <p className="text-blue-700">{question.explanation}</p>
                </div>
              </div>
            </div>
          )}

          {/* Next Button */}
          {showAnswer && (
            <div className="mt-6 text-center">
              <button
                onClick={handleNextQuestion}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
              >
                {currentQuestion >= questions.length - 1 ? '🏁 إنهاء اللعبة' : '➡️ السؤال التالي'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleQuiz;
