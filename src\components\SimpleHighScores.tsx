import React from 'react';

interface SimpleHighScoresProps {
  onBack: () => void;
}

const SimpleHighScores: React.FC<SimpleHighScoresProps> = ({ onBack }) => {
  // محاكاة بيانات أفضل النتائج
  const mockHighScores = [
    { name: 'أحمد محمد', score: 950, accuracy: 95, date: '2024-01-15' },
    { name: 'فاطمة علي', score: 890, accuracy: 89, date: '2024-01-14' },
    { name: 'محمد سالم', score: 850, accuracy: 85, date: '2024-01-13' },
    { name: 'نور الدين', score: 820, accuracy: 82, date: '2024-01-12' },
    { name: 'سارة أحمد', score: 780, accuracy: 78, date: '2024-01-11' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🏆</div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">
            أفضل النتائج
          </h2>
          <p className="text-gray-600">
            أفضل 5 نتائج في لعبة QuizRush
          </p>
        </div>

        {/* High Scores List */}
        <div className="space-y-4 mb-8">
          {mockHighScores.map((score, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border-2 transition-all ${
                index === 0
                  ? 'border-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50'
                  : index === 1
                  ? 'border-gray-400 bg-gradient-to-r from-gray-50 to-gray-100'
                  : index === 2
                  ? 'border-orange-400 bg-gradient-to-r from-orange-50 to-red-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <div className="flex items-center justify-between">
                {/* Rank and Info */}
                <div className="flex items-center gap-4">
                  <div className="text-3xl">
                    {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `#${index + 1}`}
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-800">
                      {score.score} نقطة
                    </div>
                    <div className="text-sm text-gray-600">
                      {score.name}
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex gap-6 text-center">
                  <div>
                    <div className="text-lg font-semibold text-green-600">
                      {score.accuracy}%
                    </div>
                    <div className="text-xs text-gray-500">دقة</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-purple-600">
                      {new Date(score.date).toLocaleDateString('ar-SA')}
                    </div>
                    <div className="text-xs text-gray-500">تاريخ</div>
                  </div>
                </div>
              </div>

              {/* Performance Bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      score.accuracy >= 90
                        ? 'bg-green-500'
                        : score.accuracy >= 75
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                    }`}
                    style={{ width: `${score.accuracy}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Statistics Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-6 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.max(...mockHighScores.map(s => s.score))}
            </div>
            <p className="text-sm text-gray-600">أعلى نقاط</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.max(...mockHighScores.map(s => s.accuracy))}%
            </div>
            <p className="text-sm text-gray-600">أعلى دقة</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {mockHighScores.length}
            </div>
            <p className="text-sm text-gray-600">إجمالي الألعاب</p>
          </div>
        </div>

        {/* Back Button */}
        <div className="text-center">
          <button
            onClick={onBack}
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
          >
            ← العودة للقائمة الرئيسية
          </button>
        </div>

        {/* Tips */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">💡 نصائح لتحسين نتائجك:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• اقرأ السؤال بعناية قبل اختيار الإجابة</li>
            <li>• استخدم الوقت بحكمة في الأوضاع المحددة بوقت</li>
            <li>• راجع التفسيرات لتعلم معلومات جديدة</li>
            <li>• مارس اللعب بانتظام لتحسين معلوماتك</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SimpleHighScores;
