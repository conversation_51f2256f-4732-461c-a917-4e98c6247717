import { useState } from 'react';
import type { GameMode } from './types/game.types';
import GameMenu from './components/GameMenu';
import GameModes from './components/GameModes';
import SimpleQuiz from './components/SimpleQuiz';
import SimpleHighScores from './components/SimpleHighScores';

function App() {
  const [currentScreen, setCurrentScreen] = useState('menu');
  const [selectedGameMode, setSelectedGameMode] = useState<GameMode | null>(null);

  const handleStartGame = () => {
    setCurrentScreen('modes');
  };

  const handleViewHighScores = () => {
    setCurrentScreen('highscores');
  };

  const handleSelectMode = (mode: GameMode) => {
    setSelectedGameMode(mode);
    setCurrentScreen('game');
  };

  const handleBackToMenu = () => {
    setCurrentScreen('menu');
    setSelectedGameMode(null);
  };

  const handleBackToModes = () => {
    setCurrentScreen('modes');
    setSelectedGameMode(null);
  };

  if (currentScreen === 'game' && selectedGameMode) {
    return (
      <SimpleQuiz
        gameMode={selectedGameMode}
        onBackToModes={handleBackToModes}
      />
    );
  }

  if (currentScreen === 'modes') {
    return (
      <GameModes
        onSelectMode={handleSelectMode}
        onBack={handleBackToMenu}
      />
    );
  }

  if (currentScreen === 'highscores') {
    return (
      <SimpleHighScores
        onBack={handleBackToMenu}
      />
    );
  }

  return (
    <div className="min-h-screen">
      <GameMenu
        onStartGame={handleStartGame}
        onViewHighScores={handleViewHighScores}
      />
    </div>
  );
}

export default App;
